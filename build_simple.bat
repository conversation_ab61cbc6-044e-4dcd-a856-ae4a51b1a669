@echo off
echo 🔥 SIMPLE BUILD - AUGMENT PRIVACY TOOLS
echo ========================================

REM Clean up
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist *.spec del /q *.spec

REM Install dependencies
echo 📦 Installing dependencies...
python -m pip install pyinstaller psutil watchdog pywin32 wmi

REM Create directories
if not exist shield_logs mkdir shield_logs
if not exist logs mkdir logs

echo.
echo 🛡️ Building Privacy Shield...
python -m PyInstaller --onefile --console --name "AugmentPrivacyShield" augment_privacy_shield.py

echo.
echo 🔥 Building Cleaner V2...
python -m PyInstaller --onefile --console --name "AugmentCleanerV2" augment_cleaner_v2.py

echo.
echo 📦 Creating package...
if not exist "AugmentPrivacyTools" mkdir "AugmentPrivacyTools"
if exist "dist\AugmentPrivacyShield.exe" copy "dist\AugmentPrivacyShield.exe" "AugmentPrivacyTools\"
if exist "dist\AugmentCleanerV2.exe" copy "dist\AugmentCleanerV2.exe" "AugmentPrivacyTools\"

echo.
echo ✅ BUILD COMPLETE!
echo.
echo Files created:
if exist "AugmentPrivacyTools\AugmentPrivacyShield.exe" echo ✅ AugmentPrivacyTools\AugmentPrivacyShield.exe
if exist "AugmentPrivacyTools\AugmentCleanerV2.exe" echo ✅ AugmentPrivacyTools\AugmentCleanerV2.exe

echo.
echo 🚨 USAGE:
echo 1. Run AugmentPrivacyShield.exe BEFORE using Augment
echo 2. Run AugmentCleanerV2.exe AFTER using Augment
echo 3. Both require Administrator privileges
echo.
pause
