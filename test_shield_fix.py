#!/usr/bin/env python3
"""
Test script to verify the AugmentPrivacyShield fix
"""

try:
    from augment_privacy_shield import AugmentPrivacyShield
    
    print("✅ Import successful")
    
    # Create shield instance
    shield = AugmentPrivacyShield()
    print("✅ Shield instance created")
    
    # Check if the missing methods exist
    if hasattr(shield, 'setup_data_interception'):
        print("✅ setup_data_interception method exists")
    else:
        print("❌ setup_data_interception method missing")
    
    if hasattr(shield, 'deactivate_protection'):
        print("✅ deactivate_protection method exists")
    else:
        print("❌ deactivate_protection method missing")
    
    # Test that the methods are callable
    try:
        # Test setup_data_interception (should not raise AttributeError)
        print("🧪 Testing setup_data_interception method...")
        shield.setup_data_interception()
        print("✅ setup_data_interception method works")
    except AttributeError as e:
        print(f"❌ setup_data_interception AttributeError: {e}")
    except Exception as e:
        print(f"⚠️ setup_data_interception other error (expected): {e}")
    
    try:
        # Test deactivate_protection (should not raise AttributeError)
        print("🧪 Testing deactivate_protection method...")
        shield.deactivate_protection()
        print("✅ deactivate_protection method works")
    except AttributeError as e:
        print(f"❌ deactivate_protection AttributeError: {e}")
    except Exception as e:
        print(f"⚠️ deactivate_protection other error (expected): {e}")
    
    print("\n🎉 All tests passed! The AttributeError issues have been fixed.")
    
except ImportError as e:
    print(f"❌ Import failed: {e}")
except Exception as e:
    print(f"❌ Unexpected error: {e}")
