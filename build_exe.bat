@echo off
echo 🔥 BUILDING AUGMENT PRIVACY TOOLS - MAXIMUM SECURITY EXECUTABLES
echo ================================================================
echo.

REM Clean previous builds
echo 🧹 Cleaning previous builds...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist *.spec del /q *.spec

echo.
echo 📦 Installing required dependencies...
python -m pip install pyinstaller psutil watchdog pywin32 wmi --quiet

echo.
echo � Creating required directories...
if not exist shield_logs mkdir shield_logs
if not exist logs mkdir logs

echo.
echo �🛡️ Building Privacy Shield (Real-time Protection)...
python -m PyInstaller ^
    --onefile ^
    --clean ^
    --name "AugmentPrivacyShield" ^
    --console ^
    --hidden-import=psutil ^
    --hidden-import=watchdog ^
    --hidden-import=watchdog.observers ^
    --hidden-import=watchdog.events ^
    --hidden-import=wmi ^
    --hidden-import=win32api ^
    --hidden-import=win32con ^
    --hidden-import=win32security ^
    --hidden-import=win32file ^
    --distpath dist_shield ^
    augment_privacy_shield.py

echo.
echo 🔥 Building Cleaner V2 (Advanced Fingerprint Destroyer)...
python -m PyInstaller ^
    --onefile ^
    --clean ^
    --name "AugmentCleanerV2" ^
    --console ^
    --hidden-import=psutil ^
    --hidden-import=sqlite3 ^
    --hidden-import=winreg ^
    --distpath dist_cleaner ^
    augment_cleaner_v2.py

echo.
echo 🎯 Creating portable package...
mkdir "AugmentPrivacyTools"
copy "dist_shield\AugmentPrivacyShield.exe" "AugmentPrivacyTools\"
copy "dist_cleaner\AugmentCleanerV2.exe" "AugmentPrivacyTools\"

echo.
echo ✅ BUILD COMPLETE!
echo ================================================================
echo 🛡️ Privacy Shield: AugmentPrivacyTools\AugmentPrivacyShield.exe
echo 🔥 Cleaner V2:     AugmentPrivacyTools\AugmentCleanerV2.exe
echo.
echo 🚨 USAGE INSTRUCTIONS:
echo 1. Run Privacy Shield BEFORE using Augment (real-time protection)
echo 2. Run Cleaner V2 AFTER using Augment (cleanup traces)
echo 3. Both tools require Administrator privileges for maximum effectiveness
echo.
echo 🎯 Ready for testing! These are standalone executables.
echo.
pause
